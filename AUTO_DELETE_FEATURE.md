# 🗑️ Auto Delete Spam Feature

## 📋 Overview

Fitur Auto Delete Spam memungkinkan Anda mengontrol apakah komentar yang terdeteksi sebagai spam akan dihapus secara otomatis atau memerlukan review manual terlebih dahulu.

## ⚙️ Konfigurasi

### Environment Variable
Tambahkan ke file `.env`:
```
AUTO_DELETE_SPAM=true   # Aktifkan auto delete
# AUTO_DELETE_SPAM=false # Nonaktifkan auto delete
```

### UI Controls
- **Sidebar Toggle**: Checkbox "🗑️ Auto Delete Spam" di sidebar
- **Settings Page**: Pengaturan auto delete di halaman Settings

## 🔄 Cara Kerja

### 1. Auto Delete ENABLED (Default)
```
Komentar Baru → Deteksi Spam → Confidence > Threshold → HAPUS OTOMATIS
                                                      ↓
                                              Log ke Activity Logs
```

**Behavior:**
- ✅ Komentar spam langsung dihapus otomatis
- ✅ Tidak perlu intervensi manual
- ✅ Statistik "Spam Removed" bertambah
- ✅ Log aktivitas tercatat

### 2. Auto Delete DISABLED
```
Komentar Baru → Deteksi Spam → Confidence > Threshold → SIMPAN ke Pending
                                                      ↓
                                              Butuh Review Manual
```

**Behavior:**
- ⚠️ Komentar spam TIDAK dihapus otomatis
- ⚠️ Disimpan ke "Pending Spam" untuk review
- ⚠️ Statistik "Spam Detected" bertambah
- ⚠️ Notifikasi di Dashboard

## 📊 Dashboard Indicators

### Metrics
1. **Comments Processed** - Total komentar yang diproses
2. **Spam Detected** - Total spam yang terdeteksi
3. **Spam Removed** - Total spam yang sudah dihapus
4. **Pending Review** - Spam yang menunggu review manual
5. **Monitor Status** - Status monitoring (Running/Stopped)

### Status Indicators
- **Auto Delete Status**: 🟢 Enabled / 🔴 Disabled
- **Pending Alert**: Peringatan jika ada spam pending

## 🚨 Pending Spam Page

### Fitur
- **List Pending Comments**: Daftar komentar spam yang menunggu review
- **Bulk Actions**: 
  - 🗑️ Delete All Spam
  - ✅ Mark All as Normal
- **Individual Actions**:
  - 🗑️ Delete (hapus satu komentar)
  - ✅ Mark Normal (tandai sebagai normal)

### Informasi yang Ditampilkan
- Author komentar
- Isi pesan
- Confidence score
- Waktu deteksi
- Detail prediksi AI

## 🎛️ Manual Controls

### Sidebar Controls
```
🔄 Auto Monitor
├── 🗑️ Auto Delete Spam [✓] ← Toggle ini
├── ▶️ Start Monitor
└── Statistics
```

### Comment Actions
- **Spam + Auto Delete ON**: Tampil "🤖 Auto-deleted"
- **Spam + Auto Delete OFF**: Tombol "🗑️ Delete Spam"
- **Normal Comments**: Tombol "🗑️ Delete" (manual moderation)

## 📝 Activity Logging

### Log Entries
Setiap aksi tercatat dengan detail:
```json
{
  "timestamp": "2024-01-01 12:00:00",
  "action": "DELETED",
  "comment_id": "123456",
  "author": "Username",
  "message": "Spam message...",
  "post_id": "789012",
  "reason": "Auto deletion" | "Manual deletion" | "Manual moderation"
}
```

### Reason Types
- **Auto deletion**: Dihapus otomatis oleh sistem
- **Manual deletion**: Dihapus manual dari pending spam
- **Manual moderation**: Dihapus manual oleh moderator
- **Bulk manual deletion**: Dihapus via bulk action

## 🔧 Settings Configuration

### Spam Detection Settings
- **Confidence Threshold**: Minimum confidence untuk klasifikasi spam
- **Model Path**: Path ke model IndoBERT
- **Auto Delete Toggle**: Enable/disable auto delete

### Monitor Settings
- **Poll Interval**: Interval pengecekan komentar baru
- **Auto Delete**: Pengaturan auto delete spam

## 🚀 Use Cases

### 1. Fully Automated (Recommended)
```
AUTO_DELETE_SPAM=true
CONFIDENCE_THRESHOLD=0.8
```
- Spam confidence > 80% → Hapus otomatis
- Minimal manual intervention
- Cocok untuk page dengan traffic tinggi

### 2. Manual Review
```
AUTO_DELETE_SPAM=false
CONFIDENCE_THRESHOLD=0.5
```
- Semua spam → Pending review
- Full manual control
- Cocok untuk page sensitif

### 3. Hybrid Approach
```
AUTO_DELETE_SPAM=true
CONFIDENCE_THRESHOLD=0.9
```
- Spam confidence > 90% → Hapus otomatis
- Spam 50-90% → Manual review (adjust via UI)
- Balance antara otomatis dan kontrol

## ⚠️ Important Notes

### Security
- **Backup Strategy**: Selalu backup data sebelum enable auto delete
- **Test First**: Test dengan confidence threshold rendah dulu
- **Monitor Logs**: Periksa activity logs secara berkala

### Performance
- **Pending Limit**: Sistem menyimpan max 1000 pending comments
- **Log Retention**: Activity logs disimpan max 100 entries
- **Cache Management**: Clear cache secara berkala

### Best Practices
1. **Start Conservative**: Mulai dengan auto delete OFF
2. **Monitor Accuracy**: Periksa akurasi deteksi selama beberapa hari
3. **Adjust Threshold**: Sesuaikan confidence threshold berdasarkan hasil
4. **Enable Auto Delete**: Aktifkan setelah yakin dengan akurasi
5. **Regular Review**: Periksa pending spam dan logs secara berkala

## 🔍 Troubleshooting

### Auto Delete Tidak Berfungsi
1. Periksa toggle di sidebar
2. Periksa setting AUTO_DELETE_SPAM di .env
3. Periksa confidence threshold
4. Restart aplikasi

### Terlalu Banyak False Positive
1. Naikkan confidence threshold
2. Review model training data
3. Gunakan manual review sementara

### Spam Tidak Terdeteksi
1. Turunkan confidence threshold
2. Periksa model path
3. Test dengan Test Detector page

## 📞 Support

Untuk bantuan lebih lanjut:
- Lihat Activity Logs untuk detail error
- Test detection di Test Detector page
- Periksa Pending Spam page untuk review manual
- Konsultasi dokumentasi lengkap di README_STREAMLIT.md
