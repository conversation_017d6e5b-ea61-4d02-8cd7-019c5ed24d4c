# 🔧 Auto Delete Troubleshooting Guide

## ❌ Masalah: Auto Monitor Ber<PERSON><PERSON> Tapi Spam Tidak Terhapus

### 🔍 Gejala
- Auto monitor status: "🟢 Running"
- Auto Delete: "✅ Enabled"
- Spam terdeteksi dengan confidence tinggi
- Komentar spam masih tampil dengan "🤖 Auto-deleted" tapi tidak benar-benar terhapus

### 🛠️ Penyebab & Solusi

#### 1. **Auto Monitor Tidak Benar-benar Ber<PERSON>lan**
**Penyebab**: Monitor hanya mengubah flag UI tapi tidak menjalankan background thread

**Solusi**: ✅ **SUDAH DIPERBAIKI**
- Auto monitor sekarang benar-benar menjalankan `AutoMonitor` class
- Background thread berjalan untuk monitoring real-time
- Integrasi dengan Facebook API dan spam detector

#### 2. **Facebook API Permissions**
**Penyebab**: Token tidak memiliki permission untuk menghapus komentar

**Cek**:
```bash
# Test di halaman "Test Detector" atau "Manual Check"
# Coba hapus komentar manual
```

**Solusi**:
- Pastikan Page Access Token memiliki permission:
  - `pages_manage_posts`
  - `pages_manage_engagement` 
  - `pages_read_engagement`

#### 3. **Confidence Threshold Terlalu Tinggi**
**Penyebab**: Threshold di .env berbeda dengan UI

**Cek**:
- File `.env`: `CONFIDENCE_THRESHOLD=0.5`
- UI Settings: Slider confidence threshold
- Session state: `st.session_state.confidence_threshold`

**Solusi**:
- Sesuaikan threshold di Settings page
- Atau edit `.env` dan restart aplikasi

#### 4. **Model Loading Error**
**Penyebab**: Model IndoBERT tidak ter-load dengan benar

**Cek**:
- Dashboard: Model Status harus "🟢 Loaded"
- Test Detector: Coba test dengan teks spam

**Solusi**:
- Restart aplikasi
- Periksa path model di `.env`
- Pastikan file model lengkap

## 🔄 Langkah Troubleshooting

### Step 1: Restart Auto Monitor
1. Stop monitor (⏹️ Stop Monitor)
2. Wait 5 seconds
3. Start monitor (▶️ Start Monitor)
4. Check logs for errors

### Step 2: Test Manual Delete
1. Go to "Manual Check" page
2. Select a post with spam comments
3. Try manual delete
4. If fails → Facebook API issue

### Step 3: Check Confidence Threshold
1. Go to "Test Detector" page
2. Test with known spam text
3. Check confidence score
4. Adjust threshold if needed

### Step 4: Verify Auto Delete Setting
1. Check sidebar: "🗑️ Auto Delete Spam" ✅
2. Check Settings page: Auto Delete enabled
3. Restart monitor after changing settings

### Step 5: Check Logs
1. Go to "Logs" page
2. Look for "DELETED" entries
3. Check for error messages
4. Verify deletion reasons

## 📊 Monitoring & Validation

### Dashboard Metrics
- **Comments Processed**: Should increase over time
- **Spam Detected**: Should increase when spam found
- **Spam Removed**: Should increase when auto-delete works
- **Pending Review**: Should be 0 when auto-delete enabled

### Expected Behavior
```
Auto Delete ON + Spam Detected → Immediate Deletion
├── Spam Detected: +1
├── Spam Removed: +1
├── Log Entry: "Auto deletion"
└── Comment disappears from UI
```

### Log Entries to Look For
```json
{
  "action": "DELETED",
  "reason": "Auto deletion",
  "confidence": 0.95,
  "timestamp": "2024-01-01 12:00:00"
}
```

## 🚨 Common Issues & Quick Fixes

### Issue 1: "Auto-deleted" Shows But Comment Still Visible
**Quick Fix**:
1. Refresh page (🔄 Refresh button)
2. Clear cache in Settings
3. Restart monitor

### Issue 2: No Spam Being Detected
**Quick Fix**:
1. Lower confidence threshold (Settings → 0.3)
2. Test with known spam text
3. Check model status

### Issue 3: Facebook API Errors
**Quick Fix**:
1. Regenerate Page Access Token
2. Update token in Settings
3. Test connection

### Issue 4: Monitor Stops Unexpectedly
**Quick Fix**:
1. Check browser console for errors
2. Restart application
3. Check system resources

## 🔧 Advanced Debugging

### Enable Debug Logging
```python
# Add to streamlit_app.py temporarily
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Check Session State
```python
# In Streamlit app, add debug info
st.write("Debug Info:")
st.write(f"Auto Delete Enabled: {st.session_state.auto_delete_enabled}")
st.write(f"Monitor Running: {st.session_state.monitor_running}")
st.write(f"Confidence Threshold: {st.session_state.get('confidence_threshold', 'Not set')}")
```

### Manual Test Auto Monitor
```python
# Test auto monitor directly
if 'auto_monitor' in st.session_state:
    monitor = st.session_state.auto_monitor
    status = monitor.get_status()
    st.json(status)
```

## ✅ Verification Checklist

### Pre-Flight Check
- [ ] Facebook API connected (🟢 Connected)
- [ ] Model loaded (🟢 Loaded)
- [ ] Auto Delete enabled (✅ Enabled)
- [ ] Confidence threshold appropriate (0.5-0.8)
- [ ] Monitor running (🟢 Running)

### During Operation
- [ ] Comments being processed (metric increasing)
- [ ] Spam being detected (metric increasing)
- [ ] Deletions happening (logs showing "Auto deletion")
- [ ] UI updating (spam comments disappearing)

### Post-Operation
- [ ] Statistics accurate
- [ ] Logs complete
- [ ] No pending spam (when auto-delete on)
- [ ] Performance stable

## 📞 Getting Help

### Information to Collect
1. **Dashboard Screenshot**: Show all metrics
2. **Settings Screenshot**: Show all configurations
3. **Logs Export**: Last 20 log entries
4. **Error Messages**: Any error in console/logs
5. **Test Results**: Manual delete test results

### Debug Commands
```bash
# Check application status
python test_streamlit_app.py

# Check model files
ls -la python/models/

# Check environment
cat .env | grep -E "(AUTO_DELETE|CONFIDENCE|PAGE_)"
```

## 🎯 Expected Results After Fix

### Successful Auto Delete Flow
1. **New Comment Posted** → Facebook
2. **Monitor Detects** → Background thread
3. **AI Analysis** → IndoBERT model
4. **Spam Classification** → Confidence > threshold
5. **Auto Delete** → Facebook API call
6. **UI Update** → Comment disappears
7. **Logging** → Activity recorded
8. **Statistics** → Metrics updated

### Performance Metrics
- **Detection Latency**: < 30 seconds
- **Deletion Success Rate**: > 95%
- **False Positive Rate**: < 5%
- **System Stability**: No crashes/errors

---

**🎉 Setelah perbaikan, auto delete spam seharusnya berfungsi dengan sempurna!**

**Test dengan komentar spam baru untuk memverifikasi bahwa sistem bekerja dengan benar.**
